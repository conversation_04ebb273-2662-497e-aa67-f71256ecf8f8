@import "../iconfont/fonts/font_ex";
body.has-navbar-fixed-top, html.has-navbar-fixed-top{
  padding-top:2.25rem;
}

body::-webkit-scrollbar {
  width: 1em;
}
 
body::-webkit-scrollbar-track {
  background-color: #f9f9f9;
}
 
body::-webkit-scrollbar-thumb {
  background-color: #d0d0d0;
  outline: 1px solid slategrey;
}

nav::-webkit-scrollbar {
  height: 0.75em;
}

nav::-webkit-scrollbar-track {
  background-color: #f9f9f9;
  border-radius: 5px;
}

nav::-webkit-scrollbar-thumb {
  background-color: #d0d0d0;
  //outline: 1px solid slategrey;
  border-radius: 5px;
}

code {
  color: #0087e1 !important;
}

html{
  @media screen and (max-width: 1400px) {
      font-size:0.9em;
    @media screen and (max-width: 767px) {
      font-size:0.8em;
      @media screen and (max-width: 350px) {
        font-size:0.7em;
      }
    }
  }
}

.routinga-animation-content{
  width: 100%;
  height: 100%;
  max-height: 100%;
  max-width: unset !important;
}

.modal-close:not(:hover){
  filter: drop-shadow(0 0 5px #000);
}

.icon-camera {
  font-size: 1.5em;
}

.control:last-of-type:not(:first-of-type) > *:first-child{
  border-radius: 0 4px 4px 0 !important;
}

@font-face {
  font-family: sarasa inter regular;
  src: url("~@/assets/font/Inter-Regular.woff2")
}

.code-font {
  font-family: sarasa inter regular, Consolas, monospace, Monaco, Menlo, Courier New;
}

.noscroll{
  overflow: hidden;
}

.field.is-floating-label .label {
  overflow: visible !important;
}
