@font-face {
    font-family: iconfont;
    src: url("iconfont.eot");src: url("iconfont.eot?8e0e61c2#iefix") format("embedded-opentype"),
             url("iconfont.woff2?8e0e61c2") format("woff2"),
             url("iconfont.woff?8e0e61c2") format("woff"),
             url("iconfont.ttf?8e0e61c2") format("truetype"),
             url("iconfont.svg?8e0e61c2#iconfont") format("svg");
    font-weight: normal;
    font-style: normal;
}

[class^="icon"], [class*=" icon"] {
    font-family: 'iconfont';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-style: normal;
}


.icon-Link_disconnect::before {
    content: "\554a";
}

.icon-alert::before {
    content: "\554b";
}

.icon-camera::before {
    content: "\554c";
}

.icon-caret-down::before {
    content: "\554d";
}

.icon-caret-up::before {
    content: "\554e";
}

.icon-chakandaorujilu::before {
    content: "\554f";
}

.icon-check-circle-fill::before {
    content: "\5550";
}

.icon-chuangjiangongdan1::before {
    content: "\5551";
}

.icon-clock2::before {
    content: "\5552";
}

.icon-close-circle-fill::before {
    content: "\5553";
}

.icon-cloud-sync::before {
    content: "\5554";
}

.icon-cloud::before {
    content: "\5555";
}

.icon-coding_alert_circle::before {
    content: "\5556";
}

.icon-daoru::before {
    content: "\5557";
}

.icon-daoruzupu-xianxing::before {
    content: "\5558";
}

.icon-delete::before {
    content: "\5559";
}

.icon-dian::before {
    content: "\555a";
}

.icon-earth::before {
    content: "\555b";
}

.icon-heart::before {
    content: "\555c";
}

.icon-help-circle-outline::before {
    content: "\555d";
}

.icon-info::before {
    content: "\555e";
}

.icon-label::before {
    content: "\555f";
}

.icon-lianjie::before {
    content: "\5560";
}

.icon-loading_ico::before {
    content: "\5561";
}

.icon-logout::before {
    content: "\5562";
}

.icon-setting::before {
    content: "\5563";
}

.icon-share::before {
    content: "\5564";
}

.icon-switch-menu::before {
    content: "\5565";
}

.icon-sync::before {
    content: "\5566";
}

.icon-wave::before {
    content: "\5567";
}

.icon-wendangxiugai::before {
    content: "\5568";
}

.icon-winfo-icon-chakanbaogao::before {
    content: "\5569";
}
