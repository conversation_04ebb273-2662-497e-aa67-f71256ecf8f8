package main

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/v2rayA/v2rayA/core/serverObj"
)

func main() {
	fmt.Println("Testing XHTTP protocol support in v2rayA...")
	
	// Test 1: Parse XHTTP URL
	fmt.Println("\n=== Test 1: Parse XHTTP URL ===")
	xhttpURL := "xhttp://<EMAIL>:443?type=xhttp&security=tls&path=/test&host=example.com&mode=auto#test-server"
	
	server, err := serverObj.NewXhttp(xhttpURL)
	if err != nil {
		log.Fatalf("Failed to parse XHTTP URL: %v", err)
	}
	
	xhttpServer := server.(*serverObj.Xhttp)
	fmt.Printf("Parsed server: %+v\n", xhttpServer)
	
	// Test 2: Generate configuration
	fmt.Println("\n=== Test 2: Generate Configuration ===")
	config, err := xhttpServer.Configuration(serverObj.PriorInfo{Tag: "test-tag"})
	if err != nil {
		log.Fatalf("Failed to generate configuration: %v", err)
	}
	
	configJSON, _ := json.MarshalIndent(config.CoreOutbound, "", "  ")
	fmt.Printf("Generated configuration:\n%s\n", configJSON)
	
	// Test 3: Export to URL
	fmt.Println("\n=== Test 3: Export to URL ===")
	exportedURL := xhttpServer.ExportToURL()
	fmt.Printf("Exported URL: %s\n", exportedURL)
	
	// Test 4: Parse exported URL (round-trip test)
	fmt.Println("\n=== Test 4: Round-trip Test ===")
	roundTripServer, err := serverObj.NewXhttp(exportedURL)
	if err != nil {
		log.Fatalf("Failed to parse exported URL: %v", err)
	}
	
	roundTripXhttp := roundTripServer.(*serverObj.Xhttp)
	fmt.Printf("Round-trip server: %+v\n", roundTripXhttp)
	
	// Verify key fields match
	if xhttpServer.Name != roundTripXhttp.Name ||
		xhttpServer.Server != roundTripXhttp.Server ||
		xhttpServer.Port != roundTripXhttp.Port ||
		xhttpServer.ID != roundTripXhttp.ID ||
		xhttpServer.Host != roundTripXhttp.Host ||
		xhttpServer.Path != roundTripXhttp.Path ||
		xhttpServer.Mode != roundTripXhttp.Mode {
		log.Fatalf("Round-trip test failed: fields don't match")
	}
	
	fmt.Println("\n✅ All tests passed! XHTTP protocol support is working correctly.")
	
	// Test 5: Test with VLESS protocol (since XHTTP uses VLESS)
	fmt.Println("\n=== Test 5: Test VLESS with XHTTP transport ===")
	vlessURL := "vless://<EMAIL>:8443?type=xhttp&security=reality&path=/api&host=cdn.example.com&mode=stream-up&pbk=publickey&sid=shortid#vless-xhttp-server"
	
	vlessServer, err := serverObj.NewV2Ray(vlessURL)
	if err != nil {
		log.Fatalf("Failed to parse VLESS URL with XHTTP: %v", err)
	}
	
	v2rayServer := vlessServer.(*serverObj.V2Ray)
	fmt.Printf("VLESS server with XHTTP: Net=%s, Mode=%s, Host=%s, Path=%s\n", 
		v2rayServer.Net, v2rayServer.Mode, v2rayServer.Host, v2rayServer.Path)
	
	// Generate configuration for VLESS with XHTTP
	vlessConfig, err := v2rayServer.Configuration(serverObj.PriorInfo{Tag: "vless-xhttp-tag"})
	if err != nil {
		log.Fatalf("Failed to generate VLESS configuration: %v", err)
	}
	
	vlessConfigJSON, _ := json.MarshalIndent(vlessConfig.CoreOutbound, "", "  ")
	fmt.Printf("VLESS with XHTTP configuration:\n%s\n", vlessConfigJSON)
	
	fmt.Println("\n✅ VLESS with XHTTP transport also works correctly!")
}
