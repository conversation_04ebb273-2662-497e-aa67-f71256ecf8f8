module github.com/v2rayA/v2rayA

go 1.21.0

require (
	github.com/daeuniverse/outbound v0.0.0-20230819151251-3ce60883d337
	github.com/daeuniverse/softwind v0.0.0-20230903121035-afc8c5d27a4c
)

require (
	github.com/adrg/xdg v0.4.0
	github.com/beevik/ntp v0.3.0
	github.com/devfeel/mapper v0.7.5
	github.com/dgrijalva/jwt-go/v4 v4.0.0-preview1
	github.com/gin-contrib/cors v1.6.0
	github.com/gin-gonic/gin v1.9.1
	github.com/go-leo/slicex v1.0.14
	github.com/golang/protobuf v1.5.3
	github.com/google/gopacket v1.1.19
	github.com/gorilla/websocket v1.5.0
	github.com/json-iterator/go v1.1.12
	github.com/matoous/go-nanoid v1.5.0
	github.com/miekg/dns v1.1.57
	github.com/modern-go/reflect2 v1.0.2
	github.com/mohae/deepcopy v0.0.0-201709********-c48cc78d4826
	github.com/muhammadmuzzammil1998/jsonc v0.0.0-**************-615b0916ca38
	github.com/pkg/errors v0.9.1
	github.com/sagernet/gvisor v0.0.0-**************-5fef6f2e17ab
	github.com/sagernet/sing v0.2.17
	github.com/sagernet/sing-tun v0.1.20
	github.com/shadowsocks/go-shadowsocks2 v0.1.5
	github.com/shirou/gopsutil/v3 v3.21.11
	github.com/stevenroose/gonfig v0.1.5
	github.com/tidwall/gjson v1.10.2
	github.com/tidwall/sjson v1.2.3
	github.com/v2fly/v2ray-core/v5 v5.7.0
	github.com/v2rayA/RoutingA v1.0.2
	github.com/v2rayA/beego/v2 v2.0.7
	github.com/v2rayA/shadowsocksR v1.0.4
	github.com/v2rayA/v2ray-lib v0.0.0-**************-85439332d5ce
	github.com/v2rayA/v2rayA-lib4 v0.0.0-**************-595f87cb2a49
	github.com/vearutop/statigz v1.1.7
	go.etcd.io/bbolt v1.3.8
	golang.org/x/net v0.33.0
	golang.org/x/sys v0.28.0
	google.golang.org/grpc v1.57.1
	google.golang.org/protobuf v1.33.0
)

require (
	github.com/bytedance/sonic v1.11.2 // indirect
	github.com/chenzhuoyu/base64x v0.0.0-**************-296ad89f973d // indirect
	github.com/chenzhuoyu/iasm v0.9.1 // indirect
	github.com/dgryski/go-camellia v0.0.0-**************-69a8a13fb23d // indirect
	github.com/dgryski/go-idea v0.0.0-**************-d2fb45a411fb // indirect
	github.com/dgryski/go-rc2 v0.0.0-**************-8a9021637152 // indirect
	github.com/djherbis/times v1.5.0 // indirect
	github.com/eknkc/basex v1.0.1 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.19.0 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/gocarina/gocsv v0.0.0-20210408192840-02d7211d929d // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/google/btree v1.1.2 // indirect
	github.com/google/pprof v0.0.0-20230705174524-200ffdc848b8 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/mzz2017/disk-bloom v1.0.1 // indirect
	github.com/mzz2017/quic-go v0.0.0-20230902042923-a727c1c479d4 // indirect
	github.com/onsi/ginkgo/v2 v2.11.0 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.1.1 // indirect
	github.com/pires/go-proxyproto v0.7.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/quic-go/qtls-go1-20 v0.3.3 // indirect
	github.com/riobard/go-bloom v0.0.0-20200614022211-cdc8013cb5b3 // indirect
	github.com/sagernet/go-tun2socks v1.16.12-0.20220818015926-16cb67876a61 // indirect
	github.com/sagernet/netlink v0.0.0-20220905062125-8043b4a9aa97 // indirect
	github.com/scjalliance/comshim v0.0.0-20230315213746-5e51f40bd3b9 // indirect
	github.com/shiena/ansicolor v0.0.0-20200904210342-c7312218db18 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/vishvananda/netns v0.0.0-20211101163701-50045581ed74 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	gitlab.com/yawning/chacha20.git v0.0.0-20230427033715-7877545b1b37 // indirect
	go4.org/netipx v0.0.0-20230728184502-ec4c8b891b28 // indirect
	golang.org/x/arch v0.7.0 // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/exp v0.0.0-20230626212559-97b1e661b5df // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/sync v0.10.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230807174057-1744710a1577 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

// Replace dependency modules with local developing copy
// use `go list -m all` to confirm the final module used
//replace github.com/v2rayA/shadowsocksR => ../../shadowsocksR
//replace github.com/mzz2017/go-engine => ../../go-engine
//replace github.com/v2rayA/beego/v2 => D:\beego

// replace github.com/daeuniverse/softwind => ../../softwind

replace github.com/v2fly/v2ray-core/v5 => github.com/v2rayA/v2ray-core/v5 v5.0.0-20230812170925-960565fa0686
