package serverObj

import (
	"fmt"
	"net"
	"net/url"
	"strconv"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/v2rayA/v2rayA/core/coreObj"
)

func init() {
	FromLinkRegister("xhttp", NewXhttp)
	EmptyRegister("xhttp", func() (ServerObj, error) {
		return new(Xhttp), nil
	})
}

type Xhttp struct {
	Name          string         `json:"name"`
	Server        string         `json:"server"`
	Port          int            `json:"port"`
	ID            string         `json:"id"`
	Host          string         `json:"host"`
	Path          string         `json:"path"`
	Mode          string         `json:"mode"`
	Extra         map[string]any `json:"extra"`
	TLS           string         `json:"tls"`
	SNI           string         `json:"sni"`
	Alpn          string         `json:"alpn"`
	AllowInsecure bool           `json:"allowInsecure"`
	Fingerprint   string         `json:"fingerprint"`
	PublicKey     string         `json:"publicKey"`
	ShortId       string         `json:"shortId"`
	SpiderX       string         `json:"spiderX"`
	Flow          string         `json:"flow"`
	Protocol      string         `json:"protocol"`
}

func NewXhttp(link string) (ServerObj, error) {
	return ParseXhttpURL(link)
}

func ParseXhttpURL(link string) (data *Xhttp, err error) {
	// Parse URL like: xhttp://uuid@server:port?type=xhttp&security=tls&path=/path&host=example.com#name
	u, err := url.Parse(link)
	if err != nil {
		return nil, fmt.Errorf("invalid xhttp URL format: %v", err)
	}

	port, err := strconv.Atoi(u.Port())
	if err != nil {
		return nil, fmt.Errorf("invalid port: %v", err)
	}

	data = &Xhttp{
		Name:          u.Fragment,
		Server:        u.Hostname(),
		Port:          port,
		ID:            u.User.String(),
		Host:          u.Query().Get("host"),
		Path:          u.Query().Get("path"),
		Mode:          u.Query().Get("mode"),
		TLS:           u.Query().Get("security"),
		SNI:           u.Query().Get("sni"),
		Alpn:          u.Query().Get("alpn"),
		AllowInsecure: u.Query().Get("allowInsecure") == "true",
		Fingerprint:   u.Query().Get("fp"),
		PublicKey:     u.Query().Get("pbk"),
		ShortId:       u.Query().Get("sid"),
		SpiderX:       u.Query().Get("spx"),
		Flow:          u.Query().Get("flow"),
		Protocol:      "xhttp",
	}

	// Parse extra parameters if present
	if extraStr := u.Query().Get("extra"); extraStr != "" {
		var extra map[string]any
		if err := jsoniter.UnmarshalFromString(extraStr, &extra); err == nil {
			data.Extra = extra
		}
	}

	// Set default values
	if data.Path == "" {
		data.Path = "/"
	}
	if data.Mode == "" {
		data.Mode = "auto"
	}

	return data, nil
}

func (x *Xhttp) Configuration(info PriorInfo) (c Configuration, err error) {
	core := coreObj.OutboundObject{
		Tag:      info.Tag,
		Protocol: "vless", // XHTTP is used as transport for VLESS
	}

	// Set up VLESS settings
	core.Settings.Vnext = []coreObj.Vnext{
		{
			Address: x.Server,
			Port:    x.Port,
			Users: []coreObj.User{
				{
					ID:         x.ID,
					Encryption: "none",
					Flow:       x.Flow,
				},
			},
		},
	}

	// Set up stream settings
	core.StreamSettings = &coreObj.StreamSettings{
		Network: "xhttp",
		XhttpSettings: &coreObj.XhttpSettings{
			Host:  x.Host,
			Path:  x.Path,
			Mode:  x.Mode,
			Extra: x.Extra,
		},
	}

	// Set up security (TLS/REALITY)
	switch strings.ToLower(x.TLS) {
	case "tls":
		core.StreamSettings.Security = "tls"
		core.StreamSettings.TLSSettings = &coreObj.TLSSettings{
			AllowInsecure: x.AllowInsecure,
		}
		if x.SNI != "" {
			core.StreamSettings.TLSSettings.ServerName = x.SNI
		} else if x.Host != "" {
			core.StreamSettings.TLSSettings.ServerName = x.Host
		}
		if x.Alpn != "" {
			alpn := strings.Split(x.Alpn, ",")
			for i := range alpn {
				alpn[i] = strings.TrimSpace(alpn[i])
			}
			core.StreamSettings.TLSSettings.Alpn = alpn
		}
		if x.Fingerprint != "" {
			core.StreamSettings.TLSSettings.Fingerprint = x.Fingerprint
		}
	case "reality":
		core.StreamSettings.Security = "reality"
		core.StreamSettings.RealitySettings = &coreObj.RealitySettings{
			PublicKey: x.PublicKey,
			ShortID:   x.ShortId,
			SpiderX:   x.SpiderX,
		}
		if x.SNI != "" {
			core.StreamSettings.RealitySettings.ServerName = x.SNI
		}
		if x.Fingerprint != "" {
			core.StreamSettings.RealitySettings.Fingerprint = x.Fingerprint
		}
	}

	return Configuration{
		CoreOutbound: core,
		PluginChain:  "",
		UDPSupport:   true,
	}, nil
}

func (x *Xhttp) ExportToURL() string {
	var query = make(url.Values)
	setValue(&query, "type", "xhttp")
	setValue(&query, "security", x.TLS)
	setValue(&query, "path", x.Path)
	setValue(&query, "host", x.Host)
	setValue(&query, "mode", x.Mode)

	if x.TLS != "none" && x.TLS != "" {
		setValue(&query, "flow", x.Flow)
		setValue(&query, "sni", x.SNI)
		setValue(&query, "alpn", x.Alpn)
		setValue(&query, "allowInsecure", strconv.FormatBool(x.AllowInsecure))
		setValue(&query, "fp", x.Fingerprint)
		if x.TLS == "reality" {
			setValue(&query, "pbk", x.PublicKey)
			setValue(&query, "sid", x.ShortId)
			setValue(&query, "spx", x.SpiderX)
		}
	}

	// Add extra parameters if present
	if len(x.Extra) > 0 {
		if extraBytes, err := jsoniter.Marshal(x.Extra); err == nil {
			setValue(&query, "extra", string(extraBytes))
		}
	}

	U := url.URL{
		Scheme:   "xhttp",
		User:     url.User(x.ID),
		Host:     net.JoinHostPort(x.Server, strconv.Itoa(x.Port)),
		RawQuery: query.Encode(),
		Fragment: x.Name,
	}
	return U.String()
}

func (x *Xhttp) NeedPluginPort() bool {
	return false
}

func (x *Xhttp) ProtoToShow() string {
	return "XHTTP"
}

func (x *Xhttp) GetProtocol() string {
	return x.Protocol
}

func (x *Xhttp) GetHostname() string {
	return x.Server
}

func (x *Xhttp) GetPort() int {
	return x.Port
}

func (x *Xhttp) GetName() string {
	return x.Name
}

func (x *Xhttp) SetName(name string) {
	x.Name = name
}
