package serverObj

import (
	"testing"
)

func TestParseXhttpURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected *Xhttp
		wantErr  bool
	}{
		{
			name: "basic xhttp URL",
			url:  "xhttp://<EMAIL>:443?type=xhttp&security=tls&path=/test&host=example.com&mode=auto#test-server",
			expected: &Xhttp{
				Name:     "test-server",
				Server:   "example.com",
				Port:     443,
				ID:       "uuid-123",
				Host:     "example.com",
				Path:     "/test",
				Mode:     "auto",
				TLS:      "tls",
				Protocol: "xhttp",
			},
			wantErr: false,
		},
		{
			name: "xhttp with reality",
			url:  "xhttp://uuid-456@***********:8443?type=xhttp&security=reality&path=/api&host=cdn.example.com&mode=stream-up&pbk=publickey&sid=shortid#reality-server",
			expected: &Xhttp{
				Name:      "reality-server",
				Server:    "***********",
				Port:      8443,
				ID:        "uuid-456",
				Host:      "cdn.example.com",
				Path:      "/api",
				Mode:      "stream-up",
				TLS:       "reality",
				PublicKey: "publickey",
				ShortId:   "shortid",
				Protocol:  "xhttp",
			},
			wantErr: false,
		},
		{
			name: "xhttp with default values",
			url:  "xhttp://<EMAIL>:80#simple-server",
			expected: &Xhttp{
				Name:     "simple-server",
				Server:   "test.com",
				Port:     80,
				ID:       "uuid-789",
				Path:     "/",
				Mode:     "auto",
				Protocol: "xhttp",
			},
			wantErr: false,
		},
		{
			name:    "invalid URL",
			url:     "invalid-url",
			wantErr: true,
		},
		{
			name:    "invalid port",
			url:     "xhttp://<EMAIL>:invalid#test",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseXhttpURL(tt.url)
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("ParseXhttpURL() expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("ParseXhttpURL() unexpected error: %v", err)
				return
			}
			
			if result.Name != tt.expected.Name {
				t.Errorf("Name = %v, want %v", result.Name, tt.expected.Name)
			}
			if result.Server != tt.expected.Server {
				t.Errorf("Server = %v, want %v", result.Server, tt.expected.Server)
			}
			if result.Port != tt.expected.Port {
				t.Errorf("Port = %v, want %v", result.Port, tt.expected.Port)
			}
			if result.ID != tt.expected.ID {
				t.Errorf("ID = %v, want %v", result.ID, tt.expected.ID)
			}
			if result.Host != tt.expected.Host {
				t.Errorf("Host = %v, want %v", result.Host, tt.expected.Host)
			}
			if result.Path != tt.expected.Path {
				t.Errorf("Path = %v, want %v", result.Path, tt.expected.Path)
			}
			if result.Mode != tt.expected.Mode {
				t.Errorf("Mode = %v, want %v", result.Mode, tt.expected.Mode)
			}
			if result.TLS != tt.expected.TLS {
				t.Errorf("TLS = %v, want %v", result.TLS, tt.expected.TLS)
			}
			if result.Protocol != tt.expected.Protocol {
				t.Errorf("Protocol = %v, want %v", result.Protocol, tt.expected.Protocol)
			}
		})
	}
}

func TestXhttpExportToURL(t *testing.T) {
	xhttp := &Xhttp{
		Name:     "test-server",
		Server:   "example.com",
		Port:     443,
		ID:       "uuid-123",
		Host:     "example.com",
		Path:     "/test",
		Mode:     "auto",
		TLS:      "tls",
		SNI:      "example.com",
		Protocol: "xhttp",
	}
	
	url := xhttp.ExportToURL()
	
	// Parse the generated URL to verify it's correct
	parsed, err := ParseXhttpURL(url)
	if err != nil {
		t.Errorf("Failed to parse generated URL: %v", err)
		return
	}
	
	if parsed.Name != xhttp.Name {
		t.Errorf("Name = %v, want %v", parsed.Name, xhttp.Name)
	}
	if parsed.Server != xhttp.Server {
		t.Errorf("Server = %v, want %v", parsed.Server, xhttp.Server)
	}
	if parsed.Port != xhttp.Port {
		t.Errorf("Port = %v, want %v", parsed.Port, xhttp.Port)
	}
	if parsed.ID != xhttp.ID {
		t.Errorf("ID = %v, want %v", parsed.ID, xhttp.ID)
	}
}

func TestXhttpConfiguration(t *testing.T) {
	xhttp := &Xhttp{
		Name:     "test-server",
		Server:   "example.com",
		Port:     443,
		ID:       "uuid-123",
		Host:     "example.com",
		Path:     "/test",
		Mode:     "auto",
		TLS:      "tls",
		Protocol: "xhttp",
	}
	
	info := PriorInfo{
		Tag: "test-tag",
	}
	
	config, err := xhttp.Configuration(info)
	if err != nil {
		t.Errorf("Configuration() unexpected error: %v", err)
		return
	}
	
	if config.CoreOutbound.Tag != "test-tag" {
		t.Errorf("Tag = %v, want %v", config.CoreOutbound.Tag, "test-tag")
	}
	if config.CoreOutbound.Protocol != "vless" {
		t.Errorf("Protocol = %v, want %v", config.CoreOutbound.Protocol, "vless")
	}
	if config.CoreOutbound.StreamSettings.Network != "xhttp" {
		t.Errorf("Network = %v, want %v", config.CoreOutbound.StreamSettings.Network, "xhttp")
	}
	if config.CoreOutbound.StreamSettings.XhttpSettings.Host != "example.com" {
		t.Errorf("Host = %v, want %v", config.CoreOutbound.StreamSettings.XhttpSettings.Host, "example.com")
	}
	if config.CoreOutbound.StreamSettings.XhttpSettings.Path != "/test" {
		t.Errorf("Path = %v, want %v", config.CoreOutbound.StreamSettings.XhttpSettings.Path, "/test")
	}
	if config.CoreOutbound.StreamSettings.XhttpSettings.Mode != "auto" {
		t.Errorf("Mode = %v, want %v", config.CoreOutbound.StreamSettings.XhttpSettings.Mode, "auto")
	}
	if config.CoreOutbound.StreamSettings.Security != "tls" {
		t.Errorf("Security = %v, want %v", config.CoreOutbound.StreamSettings.Security, "tls")
	}
	if !config.UDPSupport {
		t.Errorf("UDPSupport = %v, want %v", config.UDPSupport, true)
	}
}
