package antiPollution

var publicDNS = []string{
	"*******", "*******", "************", "***************", "************", "**********",
	"**************", "************", "*******", "**************", "**************", "*******",
	"**************", "************", "**********", "************", "*************",
	"**********", "**************", "**********", "*********", "*************", "************",
	"***************", "*******", "*******", "**************", "**************",
	"***************", "*************", "************", "*********", "************",
	"*************", "***********", "************", "************", "*********",
	"*************", "************", "***************",
}

func GetExternalDNS(tag string) []string {
	var external []string
	for _, dns := range publicDNS {
		external = append(external, dns+" -> "+tag)
	}
	return external
}
