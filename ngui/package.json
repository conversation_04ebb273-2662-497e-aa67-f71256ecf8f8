{"private": true, "scripts": {"dev": "nuxt dev", "build": "nuxt build", "generate": "nuxt generate", "preview": "nuxt preview", "lint": "eslint . --cache"}, "dependencies": {"@element-plus/nuxt": "^1.0.3", "@iconify-json/ri": "^1.1.5", "@nuxtjs/i18n": "8.0.0-beta.9", "@unocss/nuxt": "^0.49.4", "@vueuse/core": "^9.12.0", "@vueuse/integrations": "^9.12.0", "@vueuse/nuxt": "^9.12.0", "element-plus": "^2.2.30", "nanoid": "^4.0.1", "nuxt": "3.2.0", "qrcode": "^1.5.1", "typescript": "^4.9.5"}, "devDependencies": {"@kecrily/eslint-config": "^0.2.2", "eslint": "^8.34.0"}, "eslintConfig": {"extends": "@kecrily"}}