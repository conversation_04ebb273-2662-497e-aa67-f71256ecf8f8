; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "v2rayA"
#define MyAppVersion "TheRealVersion"
#define MyAppPublisher "v2rayA Organization"
#define MyAppURL "https://v2raya.org/"
#define MyAppExeName "v2rayA-service.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{B991C73A-599E-460D-A35A-D1ED414DADBF}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
ArchitecturesAllowed=arm64
ArchitecturesInstallIn64BitMode=arm64
DefaultDirName={autopf}\{#MyAppName}
; DisableDirPage=yes
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=yes
LicenseFile=D:\LICENSE.txt
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=D:\
OutputBaseFilename=installer_windows_inno_arm64
Compression=lzma
SolidCompression=yes
UninstallDisplayName={#MyAppName}-{#MyAppVersion}
WizardStyle=modern
SetupIconFile=D:\v2raya.ico
MinVersion=10.0.14393

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Files]
Source: "D:\v2raya-arm64-windows\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\v2raya-arm64-windows\bin\*"; DestDir: "{app}\bin"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "D:\v2raya-arm64-windows\data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "D:\v2raya-arm64-windows\v2rayA-service.xml"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\v2raya.ico"; DestDir: "{app}"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{commondesktop}\v2rayA Web Panel"; Filename: "http://localhost:2017"; IconFilename: "{app}\v2raya.ico";
Name: "{group}\v2rayA Web Panel"; Filename: "http://localhost:2017"; IconFilename: "{app}\v2raya.ico";
Name: "{group}\v2rayA Wiki"; Filename: "{#MyAppURL}";
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}";

[Run]
Filename: "{app}\{#MyAppExeName}"; Parameters: "install";
Filename: "{app}\{#MyAppExeName}"; Parameters: "start";

[UninstallRun]
Filename: "{app}\{#MyAppExeName}"; Parameters: "stop";
Filename: "{app}\{#MyAppExeName}"; Parameters: "uninstall";

[UninstallDelete]
Type: files; Name: "{app}\v2rayA-service.wrapper.log"
Type: files; Name: "{app}\v2rayA-service.out.log"
Type: files; Name: "{app}\v2rayA-service.wrapper.log"
Type: files; Name: "{app}\v2rayA-service.wrapper.log.old"
Type: files; Name: "{app}\v2rayA-service.out.log.old"
Type: files; Name: "{app}\v2rayA-service.wrapper.log.old"
Type: files; Name: "{app}\v2rayA-service.err.log"
Type: files; Name: "{app}\v2rayA-service.err.log.old"

[Code]
function InitializeSetup() : Boolean;
var 
  ResultCode: Integer;
begin
  Result := True;
  Exec('cmd.exe', '/C sc.exe stop v2rayA', '', SW_HIDE, ewWaitUntilTerminated, ResultCode); 
end;