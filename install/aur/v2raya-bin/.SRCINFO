pkgbase = v2raya-bin
	pkgdesc = A web GUI client of Project V which supports VMess, VLESS, SS, SSR, Trojan, Tuic and Juicity protocols
	pkgver = {{pkgver}}
	pkgrel = 1
	url = https://github.com/v2rayA/v2rayA
	install = .INSTALL
	arch = i686
	arch = x86_64
	arch = armv7h
	arch = loong64
	arch = aarch64
	license = AGPL3
	provides = v2raya
	conflicts = v2raya
	backup = etc/default/v2raya
	source = v2raya.service
	source = v2raya-lite.service
	source = v2raya.png
	source = v2raya.desktop
	sha1sums = {{sha_service}}
	sha1sums = {{sha_service_lite}}
	sha1sums = {{sha_png}}
	sha1sums = {{sha_desktop}}
	source_i686 = v2raya_{{pkgver}}::https://apt.v2raya.org/static/v2raya_linux_x86_{{pkgver}}
	sha1sums_i686 = {{sha1sums_i686}}
	source_x86_64 = v2raya_{{pkgver}}::https://apt.v2raya.org/static/v2raya_linux_x64_{{pkgver}}
	sha1sums_x86_64 = {{sha1sums_x86_64}}
	source_armv7h = v2raya_{{pkgver}}::https://apt.v2raya.org/static/v2raya_linux_armv7_{{pkgver}}
	sha1sums_armv7h = {{sha1sums_armv7h}}
	source_loong64 = v2raya_{{pkgver}}::https://apt.v2raya.org/static/v2raya_linux_loongarch64_{{pkgver}}
	sha1sums_loong64 = {{sha1sums_loong64}}
	source_aarch64 = v2raya_{{pkgver}}::https://apt.v2raya.org/static/v2raya_linux_arm64_{{pkgver}}
	sha1sums_aarch64 = {{sha1sums_aarch64}}

pkgname = v2raya-bin
	depends = v2ray>=5.0.0
