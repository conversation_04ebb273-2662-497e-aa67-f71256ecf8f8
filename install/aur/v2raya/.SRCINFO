pkgbase = v2raya
	pkgdesc = A web GUI client of Project V which supports VMess, VLESS, SS, SSR, Trojan, Tuic and Juicity protocols
	pkgver = {{pkgver}}
	pkgrel = 1
	url = https://github.com/v2rayA/v2rayA
	install = .INSTALL
	arch = i686
	arch = pentium4
	arch = x86_64
	arch = arm
	arch = armv7h
	arch = armv6h
	arch = aarch64
	license = AGPL3
	makedepends = git
	makedepends = go>=2:1.17.0-1
	makedepends = nodejs>=14
	makedepends = yarn
	depends = glibc
	backup = etc/default/v2raya
	source = v2raya-{{pkgver}}.tar.gz::https://github.com/v2rayA/v2rayA/archive/v{{pkgver}}.tar.gz
	sha512sums = SKIP

pkgname = v2raya
	depends = glibc
	depends = v2ray>=5.0.0
