#!/usr/bin/expect -f

set timeout 30

set passphrase "$env(SIGNING_PASSWORD)"

# Call ssh-add with variable length arguments, so that this script
# takes the same arguments as the original program
spawn ssh-add {*}$argv

expect {
    timeout                     {send_error "\nFailed to get password prompt\n";
                                 exit 1}
    "*assphrase*"  {send -- "$passphrase\r";
                                 send_user " *** entering passphrase ***";
                                 exp_continue}
}

# Get the pid, spawnid, oserr and exitcode from the spawned ssh-add command
set returnvalues [wait]

# Extract the ssh-add exit code
set exitcode [lindex $returnvalues 3]

# Exit with the exitcode from ssh-add (0 on success)
exit $exitcode