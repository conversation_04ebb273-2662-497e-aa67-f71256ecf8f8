[Unit]
Description=v2rayA Lite Service
Documentation=https://github.com/v2rayA/v2rayA/wiki
After=network.target network-online.target nss-lookup.target iptables.service ip6tables.service nftables.service
Wants=network.target

[Service]
Type=simple
ExecStart=/usr/bin/v2raya --lite --log-disable-timestamp
Environment=V2RAYA_LOG_FILE=%L/v2raya/v2raya.log
Restart=on-failure

[Install]
WantedBy=default.target
