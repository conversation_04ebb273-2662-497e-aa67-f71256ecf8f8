[Unit]
Description=v2rayA Service
Documentation=https://github.com/v2rayA/v2rayA/wiki
After=network.target network-online.target nss-lookup.target iptables.service ip6tables.service nftables.service
Wants=network.target

[Service]
Type=simple
User=root
LimitNPROC=500
LimitNOFILE=1000000
ExecStart=/usr/bin/v2raya --log-disable-timestamp
Environment=V2RAYA_LOG_FILE=/var/log/v2raya/v2raya.log
EnvironmentFile=-/etc/default/v2raya
Restart=on-failure

[Install]
WantedBy=multi-user.target
