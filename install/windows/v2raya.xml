<!--
  MIT License

  Copyright (c) 2008-2020 <PERSON><PERSON><PERSON>, Sun Microsystems, Inc., CloudBees,
  Inc., <PERSON><PERSON> and other contributors

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to deal
  in the Software without restriction, including without limitation the rights
  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in all
  copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  SOFTWARE.
-->

<service>

  <id>v2rayA</id>
  <name>v2rayA</name>
  <description>v2rayA is a V2Ray client, compatible with SS, SSR, Trojan(-go), Tuic and Juicity protocols.</description>
    <env name="V2RAYA_CONFIG" value="%BASE%"/>
    <env name="V2RAY_LOCATION_ASSET" value="%BASE%"/>
  <executable>%BASE%\v2raya_windows_@ARCH@_@VERSION@.exe</executable>
  <arguments>--lite --log-disable-timestamp --log-file v2raya.log --v2ray-bin "%BASE%\v2ray-core\v2ray.exe"</arguments>
  <onfailure action="restart" delay="10 sec"/>
  <log mode="none"/>

</service>
