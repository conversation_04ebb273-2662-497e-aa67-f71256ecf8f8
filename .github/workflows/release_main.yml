name: Build & Release v2rayA
on:
  workflow_dispatch:
    inputs:
      tag:
        type: string
        required: true
jobs:
  Build_v2rayA_Web:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Node.js
      shell: bash
      run: |
        eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
        brew install node@20
        echo "PATH=\"$(brew --prefix)/opt/node@20/bin:$PATH\"" >> $GITHUB_ENV
        echo "PATH=\"$(brew --prefix)/opt/node@20/bin:$PATH\"" >> ~/.bash_profile
    - name: Install Dependencies
      run: |
        sudo apt-get update -y && sudo apt-get install -y gzip
    - name: Build GUI
      run: |
        yarn --cwd gui --check-files
        yarn --cwd gui build
        echo "Use tar to generate web.tar.gz..."
        tar -zcvf web.tar.gz web/
    - name: Upload Zip File to Artifacts
      uses: actions/upload-artifact@v4
      with:
        path: web/*
        name: web
    - name: Upload the tar archive to Artifacts
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: |
          web.tar.gz
  Build_v2rayA_Binaries:
    runs-on: ubuntu-latest
    needs: [Build_v2rayA_Web]
    env:
      CGO_ENABLED: 0
      NAME: v2raya
      DESC: "A web GUI client of Project V which supports VMess, VLESS, SS, SSR, Trojan, Tuic and Juicity protocols"
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Download Artifact
      uses: actions/download-artifact@v4
      with:
        name: web
        path: service/server/router/web
    - name: Check Version
      id: prep
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        cache-dependency-path: |
          service/go.mod
          service/go.sum
        go-version: ^1.21
    - name: Build v2rayA Binaries
      id: build
      shell: pwsh
      run: |
        New-Item -ItemType Directory -Path v2raya_binaries
        foreach ($arch in @('amd64', 'arm64', '386', 'riscv64', 'mips64', 'mips64le', 'mipsle', 'mips', 'loong64')) {
          $env:GOARCH = $arch
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."linux-$arch")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION} -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
        foreach ($arm in @('7')) {
          $env:GOARCH = 'arm'
          $env:GOARM = $arm
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."linux-arm$arm")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION} -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
        foreach ($arch in @('amd64', 'arm64')) {
          $env:GOOS = 'windows'
          $env:GOARCH = $arch
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."windows-$arch")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION}.exe -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
        foreach ($arch in @('amd64', 'arm64')) {
          $env:GOOS = 'darwin'
          $env:GOARCH = $arch
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."darwin-$arch")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION} -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
        foreach ($arch in @('amd64', 'arm64')) {
          $env:GOOS = 'freebsd'
          $env:GOARCH = $arch
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."freebsd-$arch")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION} -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
        foreach ($arch in @('amd64', 'arm64')) {
          $env:GOOS = 'openbsd'
          $env:GOARCH = $arch
          $filename = $((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."openbsd-$arch")."friendlyName"
          Set-Location -Path service
          go build -tags "with_gvisor" -o ../v2raya_binaries/v2raya_${filename}_${env:VERSION} -ldflags="-X github.com/v2rayA/v2rayA/conf.Version=${env:VERSION} -s -w" -trimpath
          Set-Location -Path ..
        }
    - name: Upload Artifact
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: |
          v2raya_binaries/*
  Build_Windows_Installers:
    runs-on: windows-latest
    needs: [Build_v2rayA_Binaries]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      shell: bash
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Download Artifact Windows x64
      uses: actions/download-artifact@v4
      with:
        name: v2raya_windows_x64_${{ steps.prep.outputs.VERSION }}.exe
        path: D:\Downloads
    - name: Download Artifact Windows arm64
      uses: actions/download-artifact@v4
      with:
        name: v2raya_windows_arm64_${{ steps.prep.outputs.VERSION }}.exe
        path: D:\Downloads
    - name: Install Inno Setup
      shell: pwsh
      run: |
        choco install innosetup -y
    - name: Build Windows Installer
      shell: pwsh
      run: |
        ## Create Destination Directory
        New-Item -ItemType Directory -Path "D:\v2raya-x86_64-windows\data"
        New-Item -ItemType Directory -Path "D:\v2raya-x86_64-windows\bin"
        New-Item -ItemType Directory -Path "D:\v2raya-arm64-windows\data"
        New-Item -ItemType Directory -Path "D:\v2raya-arm64-windows\bin"
        ## Copy v2rayA to Destination Directory
        Copy-Item -Path D:\Downloads\v2raya_windows_arm64_${{ steps.prep.outputs.VERSION }}.exe -Destination D:\v2raya-arm64-windows\bin\v2raya.exe
        Copy-Item -Path D:\Downloads\v2raya_windows_x64_${{ steps.prep.outputs.VERSION }}.exe -Destination D:\v2raya-x86_64-windows\bin\v2raya.exe
        Copy-Item -Path ".\install\windows-inno\v2raya.ico" -Destination "D:\v2raya.ico"
        ## Download and extract v2ray
        $Url_v2ray_x64 = "https://github.com/v2fly/v2ray-core/releases/latest/download/v2ray-windows-64.zip"
        $Url_v2ray_A64 = "https://github.com/v2fly/v2ray-core/releases/latest/download/v2ray-windows-arm64-v8a.zip"
        Invoke-WebRequest $Url_v2ray_x64 -OutFile "D:\v2ray-windows-x64.zip"
        Expand-Archive -Path "D:\v2ray-windows-x64.zip" -DestinationPath "D:\v2raya-x86_64-windows\bin\"
        Move-Item -Path "D:\v2raya-x86_64-windows\bin\*.dat" -Destination "D:\v2raya-x86_64-windows\data\"
        Remove-Item -Path "D:\v2raya-x86_64-windows\bin\*.json" -Force -Recurse -ErrorAction SilentlyContinue
        Invoke-WebRequest $Url_v2ray_A64 -OutFile "D:\v2ray-windows-A64.zip"
        Expand-Archive -Path "D:\v2ray-windows-A64.zip" -DestinationPath "D:\v2raya-arm64-windows\bin\"
        Move-Item -Path "D:\v2raya-arm64-windows\bin\*.dat" -Destination "D:\v2raya-arm64-windows\data\"
        Remove-Item -Path "D:\v2raya-arm64-windows\bin\*.json" -Force -Recurse -ErrorAction SilentlyContinue
        ## Download WinSW
        ## WinSW said they have a native ARM64 version, but I cannot find it, so use net4 version instead on ARM-based Windows.
        ## See more in "https://visualstudiomagazine.com/articles/2022/08/12/net-framework-4-8-1.aspx"
        $Url_WinSW = "https://github.com/winsw/winsw/releases/download/v3.0.0-alpha.11/WinSW-net461.exe"
        Invoke-WebRequest $Url_WinSW -OutFile "D:\WinSW.exe"
        Copy-Item -Path "D:\WinSW.exe" -Destination "D:\v2raya-x86_64-windows\v2rayA-service.exe"
        Copy-Item -Path "D:\WinSW.exe" -Destination "D:\v2raya-arm64-windows\v2rayA-service.exe"
        ## Copy License and Service Config
        Copy-Item -Path ".\LICENSE" -Destination "D:\LICENSE.txt"
        Copy-Item -Path ".\install\windows-inno\v2rayA-service.xml" -Destination "D:\v2raya-x86_64-windows\v2rayA-service.xml"
        Copy-Item -Path ".\install\windows-inno\v2rayA-service.xml" -Destination "D:\v2raya-arm64-windows\v2rayA-service.xml"
        ## Set Version
        $(Get-Content -Path .\install\windows-inno\windows_x86_64.iss).replace("TheRealVersion", "${{ steps.prep.outputs.VERSION }}") | Out-File "D:\windows_x86_64.iss"
        $(Get-Content -Path .\install\windows-inno\windows_arm64.iss).replace("TheRealVersion", "${{ steps.prep.outputs.VERSION }}") | Out-File "D:\windows_arm64.iss"
        ## Build Installer
        & 'C:\Program Files (x86)\Inno Setup 6\ISCC.exe' "D:\windows_x86_64.iss"
        & 'C:\Program Files (x86)\Inno Setup 6\ISCC.exe' "D:\windows_arm64.iss"
        ## Rename to Friendly Name
        Copy-Item -Path D:\installer_windows_inno_x64.exe -Destination .\installer_windows_inno_x64_${{ steps.prep.outputs.VERSION }}.exe
        Copy-Item -Path D:\installer_windows_inno_arm64.exe -Destination .\installer_windows_inno_arm64_${{ steps.prep.outputs.VERSION }}.exe
    - name: Upload Artifact
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: |
          installer_windows_inno_x64_${{ steps.prep.outputs.VERSION }}.exe
          installer_windows_inno_arm64_${{ steps.prep.outputs.VERSION }}.exe
  Build_Linux_Packages:
    runs-on: ubuntu-22.04
    needs: [Build_v2rayA_Binaries]
    strategy:
      matrix:
        goos: [linux]
        goarch: [amd64, arm64, 386, riscv64, mips64, mips64le, mipsle, mips, loong64]
        include:
          - goos: linux
            goarch: arm
            goarm: 7
    env:
      GOOS: ${{ matrix.goos }}
      GOARCH: ${{ matrix.goarch }}
      GOARM: ${{ matrix.goarm }}
      CGO_ENABLED: 0
      NAME: v2raya
      DESC: "A web GUI client of Project V which supports VMess, VLESS, SS, SSR, Trojan, Tuic and Juicity protocols"
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Get Friendly File Name
      shell: pwsh
      id: get_filename
      run: |
        $build_name = $(((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."${{ matrix.goos }}-${{ matrix.goarch }}${{ matrix.goarm }}").friendlyName)
        $friendly_arch = $((((Get-Content ./install/friendly-filenames.json | ConvertFrom-Json)."${{ matrix.goos }}-${{ matrix.goarch }}${{ matrix.goarm }}").friendlyName).Split('_')[1])
        Write-Output "BUILD_NAME=$build_name" >> ${env:GITHUB_OUTPUT}
        Write-Output "BUILD_NAME=$build_name" >> ${env:GITHUB_ENV}
        Write-Output "FRIENDLY_ARCH=$friendly_arch" >> ${env:GITHUB_OUTPUT}
        Write-Output "FRIENDLY_ARCH=$friendly_arch" >> ${env:GITHUB_ENV}
    - name: Check Version
      id: prep
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
        echo "PACKAGE_VERSION=$version" >> $GITHUB_OUTPUT
        echo "PACKAGE_VERSION=$version" >> $GITHUB_ENV
    - name: Download Artifact
      uses: actions/download-artifact@v4
      with:
        name: v2raya_${{ steps.get_filename.outputs.BUILD_NAME }}_${{ steps.prep.outputs.VERSION }}
        path: build/
    - name: Download x64 Linux Build
      uses: actions/download-artifact@v4
      with:
        name: v2raya_linux_x64_${{ steps.prep.outputs.VERSION }}
        path: test/
    - name: Show Artifacts
      run: |
        ls -l build/
    - name: Build Packages
      run: |
        sudo gem install fpm -v 1.15.1
        sudo apt-get install -y libarchive-tools rpm tar unzip
        sudo chmod 755 ./test/v2raya_linux_x64_$VERSION
        ./test/v2raya_linux_x64_$VERSION --report config | sed '1,6d' | fold -s -w 78 | sed -E 's/^([^#].+)/# \1/' >> install/universal/v2raya.default
        if [ -n "$(echo $GOARCH | grep mips)" ]; then
          packages="deb rpm"
        else
          packages="deb rpm pacman"
        fi
        sudo chmod 755 ./build/v2raya_${{ steps.get_filename.outputs.BUILD_NAME }}_${{ steps.prep.outputs.VERSION }}
        for package_manager in $packages; do
          if [ "$package_manager" == 'pacman' ];then
            if [ "$GOARCH" == 'arm' ] && [ "$GOARM" == '7' ];then
              package_arch='arm7hf'
            elif [ "$GOARCH" == 'arm64' ];then
              package_arch='aarch64'
            else
              package_arch="$GOARCH"
            fi
          elif [ "$package_manager" == 'rpm' ];then
            if [ "$GOARCH" == 'arm' ] && [ "$GOARM" == '7' ];then
              package_arch='armhfp'
            elif [ "$GOARCH" == 'arm64' ];then
              package_arch='aarch64'
            elif [ "$GOARCH" == 'loong64' ];then
              package_arch='loongson64'
            else
              package_arch="$GOARCH"
            fi
          elif [ "$package_manager" == 'deb' ];then
            if [ "$GOARCH" == 'arm' ] && [ "$GOARM" == '7' ];then
              package_arch='armhf'
            elif [ "$GOARCH" == '386' ];then
              package_arch='i386'
            elif [ "$GOARCH" == 'mipsle' ];then
              package_arch='mips32le'
            else
              package_arch="$GOARCH"
            fi
          fi
            fpm -s dir -t "$package_manager" -a $package_arch --version "${{ steps.prep.outputs.PACKAGE_VERSION }}" \
            --url 'https://github.com/v2rayA/v2rayA' --description "$DESC" \
            --maintainer "<EMAIL>" --name v2raya --license 'AGPL' \
            --package installer_linux_$GOARCH$GOAMD64$GOARM_${{ steps.prep.outputs.VERSION }}.$package_manager \
            --after-install ./install/universal/after_install.sh \
            --after-upgrade ./install/universal/after_upgrade.sh \
            ./build/v2raya_${{ steps.get_filename.outputs.BUILD_NAME }}_${{ steps.prep.outputs.VERSION }}=/usr/bin/v2raya \
            ./install/universal/v2raya.service=/usr/lib/systemd/system/v2raya.service \
            ./install/universal/v2raya-lite.service=/usr/lib/systemd/user/v2raya-lite.service \
            ./install/universal/v2raya.png=/usr/share/icons/hicolor/512x512/apps/v2raya.png \
            ./install/universal/v2raya.desktop=/usr/share/applications/v2raya.desktop \
            ./install/universal/v2raya.default=/etc/default/v2raya
        done
        mkdir fpm_packages
        [ -f installer_linux_$GOARCH$GOAMD64$GOARM_${{ steps.prep.outputs.VERSION }}.pacman ] && \
        mv installer_linux_$GOARCH$GOAMD64$GOARM_${{ steps.prep.outputs.VERSION }}.pacman \
           fpm_packages/installer_archlinux_${{ steps.get_filename.outputs.FRIENDLY_ARCH }}_${{ steps.prep.outputs.VERSION }}.pkg.tar.zst
        mv installer_linux_$GOARCH$GOAMD64$GOARM_${{ steps.prep.outputs.VERSION }}.rpm \
           fpm_packages/installer_redhat_${{ steps.get_filename.outputs.FRIENDLY_ARCH }}_${{ steps.prep.outputs.VERSION }}.rpm
        mv installer_linux_$GOARCH$GOAMD64$GOARM_${{ steps.prep.outputs.VERSION }}.deb \
           fpm_packages/installer_debian_${{ steps.get_filename.outputs.FRIENDLY_ARCH }}_${{ steps.prep.outputs.VERSION }}.deb
    - name: Upload Artifact
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: |
          fpm_packages/*
  GitHub_Release:
    runs-on: ubuntu-22.04
    needs: [Build_v2rayA_Binaries, Build_Windows_Installers, Build_Linux_Packages] 
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Download Artifacts
      uses: actions/download-artifact@v4
      with:
        path: builds/
    - name: Move Files to Release Directory
      shell: bash
      run: |
        mkdir -p release
        for file in $(ls builds | grep -E "^installer|^v2raya"); do
          mv builds/$file/$file release/
        done
        mv builds/web.tar.gz/web.tar.gz release/
        for file in $(ls release); do
          sha256sum release/$file | awk '{print $1}' > release/$file.sha256.txt
        done
        ls -l release/
    - name: Compress Web Files
      shell: pwsh
      run: |
        Compress-Archive -Path ./builds/web/* -DestinationPath ./web.zip
        ((Get-FileHash ./web.zip).hash).ToLower() > web.zip.sha256.txt
        Move-Item ./web.zip ./release/web.zip
        Move-Item ./web.zip.sha256.txt ./release/web.zip.sha256.txt
    - name: Upload Release
      env:
        GH_TOKEN: ${{ github.token }}
      run: |
        echo "Don't upload SingTun binaries to GitHub Release for they are not stable."
        rm -f release/v2raya_with_singtun_*
        Latest_Releases=$(curl -s -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/v2rayA/v2rayA/releases/latest | jq -r '.tag_name')
        Latest_tag=v${{ steps.prep.outputs.VERSION }}
        if [ "$Latest_Releases" != "$Latest_tag" ]; then
          gh release create "v${{ steps.prep.outputs.VERSION }}" -t "v${{ steps.prep.outputs.VERSION }}" --generate-notes
        fi
        gh release upload "v${{ steps.prep.outputs.VERSION }}" release/*
    - name: Refresh Cloudflare Cache
      env:
        CF_AUTH_EMAIL: ${{ secrets.CF_AUTH_EMAIL }}
        CF_PARGE_CACHE_AUTH_KEY: ${{ secrets.CF_PARGE_CACHE_AUTH_KEY }}
        CF_ZONE: ${{ secrets.CF_ZONE }}
      shell: bash
      run: |
        curl -X POST "https://api.cloudflare.com/client/v4/zones/$CF_ZONE/purge_cache" \
          -H "X-Auth-Email: $CF_AUTH_EMAIL" \
          -H "Authorization: Bearer $CF_PARGE_CACHE_AUTH_KEY" \
          -H "Content-Type: application/json" \
          --data '{"purge_everything":true}'
  Build_v2ray_Debian_Packages:
    runs-on: ubuntu-22.04
    steps:
    - name: Install Tools
      run: |
        sudo apt-get update -y && sudo apt-get install -y wget curl unzip
        sudo gem install fpm -v 1.15.1
    - name: Check Version
      run: |
        v2ray_temp_file="./v2ray.version.temp"
        if ! curl -s -I "https://github.com/v2fly/v2ray-core/releases/latest" > "$v2ray_temp_file"; then
            echo "${RED}Error: Cannot get latest version of v2ray!${RESET}"
            exit 1
        fi
        v2ray_version=$(grep -i ^location: "$v2ray_temp_file" | awk '{print $2}' | tr -d '\r' | awk -F 'tag/' '{print $2}')
        rm -f "$v2ray_temp_file"
        xray_temp_file="./xray.version.temp"
        if ! curl -s -I "https://github.com/XTLS/Xray-core/releases/latest" > "$xray_temp_file"; then
            echo "${RED}Error: Cannot get latest version of xray!${RESET}"
            exit 1
        fi
        xray_version=$(grep -i ^location: "$xray_temp_file" | awk '{print $2}' | tr -d '\r' | awk -F 'tag/' '{print $2}')
        rm -f "$xray_temp_file"
        echo "V2RAY_VERSION=$v2ray_version" >> $GITHUB_OUTPUT
        echo "XRAY_VERSION=$xray_version" >> $GITHUB_OUTPUT
        echo "V2RAY_VERSION=$v2ray_version" >> $GITHUB_ENV
        echo "XRAY_VERSION=$xray_version" >> $GITHUB_ENV
    - name: Download v2ray and Xray
      run: |
        mkdir xray_unzipped
        mkdir v2ray_unzipped
        for arch in 64 32 arm64-v8a arm32-v7a mips32 mips32le mips64 mips64le riscv64 loong64; do
          wget -q https://github.com/v2fly/v2ray-core/releases/download/$V2RAY_VERSION/v2ray-linux-$arch.zip
          unzip -q v2ray-linux-$arch.zip -d v2ray_unzipped/v2ray-linux-$arch
          wget -q https://github.com/XTLS/xray-core/releases/download/$XRAY_VERSION/Xray-linux-$arch.zip
          unzip -q Xray-linux-$arch.zip -d xray_unzipped/xray-linux-$arch
        done
    - name: Build v2ray Packages
      run: |
        mkdir v2ray_packages
        package_version="$(echo "$V2RAY_VERSION" | sed 's/v//g')" # Remove the leading 'v' in version number
        for arch in amd64 i386 arm64 armhf mips mips32le mips64 mips64le riscv64 loong64; do
        case $arch in
          amd64)
            source_dir="v2ray_unzipped/v2ray-linux-64"
            ;;
          i386)
            source_dir="v2ray_unzipped/v2ray-linux-32"
            ;;
          arm64)
            source_dir="v2ray_unzipped/v2ray-linux-arm64-v8a"
            ;;
          armhf)
            source_dir="v2ray_unzipped/v2ray-linux-arm32-v7a"
            ;;
          mips)
            source_dir="v2ray_unzipped/v2ray-linux-mips32"
            ;;
          mips32le)
            source_dir="v2ray_unzipped/v2ray-linux-mips32le"
            ;;
          mips64)
            source_dir="v2ray_unzipped/v2ray-linux-mips64"
            ;;
          mips64le)
            source_dir="v2ray_unzipped/v2ray-linux-mips64le"
            ;;
          riscv64)
            source_dir="v2ray_unzipped/v2ray-linux-riscv64"
            ;;
          loong64)
            source_dir="v2ray_unzipped/v2ray-linux-loong64"
            ;;
        esac
        echo 'systemctl daemon-reload
        if [ "$(systemctl is-active v2ray)" = "active" ]; then
            systemctl restart v2ray
        fi' > $source_dir/postinst
        echo "systemctl daemon-reload" >> $source_dir/post-remove
        sed -i 's|usr/local/bin/v2ray|usr/bin/v2ray|g' $source_dir/systemd/system/v2ray.service
        sed -i 's|usr/local/bin/v2ray|usr/bin/v2ray|g' $source_dir/systemd/system/v2ray@.service
        sed -i 's|usr/local/etc/v2ray|etc/v2ray|g' $source_dir/systemd/system/v2ray.service
        sed -i 's|usr/local/etc/v2ray|etc/v2ray|g' $source_dir/systemd/system/v2ray@.service
        fpm -s dir -t deb -a $arch --version $package_version \
          --url 'https://v2fly.org/' --description 'A platform for building proxies to bypass network restrictions' \
          --maintainer "<EMAIL>" --name v2ray --license 'MIT' \
          --package ./v2ray_packages/v2ray_"$arch"_"$package_version".deb \
          --after-install $source_dir/postinst \
          --after-remove $source_dir/post-remove \
          $source_dir/v2ray=/usr/bin/v2ray \
          $source_dir/systemd/system/v2ray.service=/usr/lib/systemd/system/v2ray.service \
          $source_dir/systemd/system/v2ray@.service=/usr/lib/systemd/system/v2ray@.service \
          $source_dir/geoip.dat=/usr/share/v2ray/geoip.dat \
          $source_dir/geoip-only-cn-private.dat=/usr/share/v2ray/geoip-only-cn-private.dat \
          $source_dir/geosite.dat=/usr/share/v2ray/geosite.dat
        done
    - name: Build Xray Packages
      run: |
        mkdir xray_packages
        package_version="$(echo "$XRAY_VERSION" | sed 's/v//g')" # Remove the leading 'v' in version number
        for arch in amd64 i386 arm64 armhf mips mips32le mips64 mips64le riscv64 loong64; do
        case $arch in
          amd64)
            source_dir="xray_unzipped/xray-linux-64"
            ;;
          i386)
            source_dir="xray_unzipped/xray-linux-32"
            ;;
          arm64)
            source_dir="xray_unzipped/xray-linux-arm64-v8a"
            ;;
          armhf)
            source_dir="xray_unzipped/xray-linux-arm32-v7a"
            ;;
          mips)
            source_dir="xray_unzipped/xray-linux-mips32"
            ;;
          mips32le)
            source_dir="xray_unzipped/xray-linux-mips32le"
            ;;
          mips64)
            source_dir="xray_unzipped/xray-linux-mips64"
            ;;
          mips64le)
            source_dir="xray_unzipped/xray-linux-mips64le"
            ;;
          riscv64)
            source_dir="xray_unzipped/xray-linux-riscv64"
            ;;
          loong64)
            source_dir="xray_unzipped/xray-linux-loong64"
            ;;
        esac
        fpm -s dir -t deb -a $arch --version $package_version \
          --url 'https://xtls.github.io/' --description 'Xray, Penetrates Everything. Also the best v2ray-core, with XTLS support. Fully compatible configuration' \
          --maintainer "<EMAIL>" --name xray --license 'MPL-2.0' \
          --package ./xray_packages/xray_"$arch"_"$package_version".deb \
          $source_dir/xray=/usr/bin/xray \
          $source_dir/geoip.dat=/usr/share/xray/geoip.dat \
          $source_dir/geosite.dat=/usr/share/xray/geosite.dat
        done
    - name: Note Version Number
      run: |
        echo "V2RAY_VERSION=$V2RAY_VERSION" >> ./v2ray_packages_version.txt
        echo "XRAY_VERSION=$XRAY_VERSION" >> ./xray_packages_version.txt
    - name: Upload Artifact
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: |
          v2ray_packages/*
          xray_packages/*
          v2ray_packages_version.txt
          xray_packages_version.txt
  Build_APT_Repository_and_AUR:
    runs-on: ubuntu-22.04
    needs: [Build_v2rayA_Binaries, Build_Linux_Packages, Build_v2ray_Debian_Packages]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      env:
        REF: ${{ inputs.tag }}
      run: |
        echo "P_DIR=$(pwd)" >> $GITHUB_OUTPUT
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Download Artifacts
      uses: actions/download-artifact@v4
      with:
        path: builds/
    - name: Prepare Builds
      run: |
        sudo apt install -y lintian reprepro gnupg gnupg2 expect >> /dev/null
        mv builds/web.tar.gz/web.tar.gz ./web.tar.gz
        v2ray_version="$(cat builds/v2ray_packages_version.txt/v2ray_packages_version.txt | awk -F 'v' '{print $2}')"
        echo "v2ray core version: $v2ray_version"
        xray_version="$(cat builds/xray_packages_version.txt/xray_packages_version.txt | awk -F 'v' '{print $2}')"
        echo "xray core version: $xray_version"
        for deb_File in $(ls builds | grep -E "^v2ray_"); do
          mv builds/$deb_File/$deb_File ./$deb_File
        done
        for deb_File in $(ls builds | grep -E "^xray_"); do
          mv builds/$deb_File/$deb_File ./$deb_File
        done
        for deb_File in $(ls builds | grep -E "^installer_"); do
          mv builds/$deb_File/$deb_File ./$deb_File
        done
        for v2raya_bin in $(ls builds | grep -E "^v2raya_linux"); do
          mv builds/"$v2raya_bin"/"$v2raya_bin" ./"$v2raya_bin"
        done
        ls -lh ./
        echo "V2RAY_VERSION=$v2ray_version" >> $GITHUB_OUTPUT
        echo "XRAY_VERSION=$xray_version" >> $GITHUB_OUTPUT
        echo "V2RAY_VERSION=$v2ray_version" >> $GITHUB_ENV
        echo "XRAY_VERSION=$xray_version" >> $GITHUB_ENV
    - name: Import GPG key
      id: import_gpg
      uses: crazy-max/ghaction-import-gpg@v5.4.0
      with:
        gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
        passphrase: ${{ secrets.SIGNING_PASSWORD }}
    - name: Generate APT Repository
      env:
        SIGNING_PASSWORD: ${{ secrets.SIGNING_PASSWORD }}
      run: |
        mkdir -p apt/incoming && cp -r install/debian/key install/debian/conf apt/
        install/tool/reprepro_expect --ask-passphrase -Vb apt includedeb v2raya installer_debian_*_$VERSION.deb xray_*_$XRAY_VERSION.deb v2ray_*_$V2RAY_VERSION.deb 
        install/tool/reprepro_expect --ask-passphrase -Vb apt export       
        mkdir apt/static/
        cp v2raya_*_*_${{ steps.prep.outputs.VERSION }}* apt/static/
        cp web.tar.gz apt/static/web_$VERSION.tar.gz
        echo "apt.v2raya.org" > apt/CNAME
    - name: Deploy APT Repository
      uses: crazy-max/ghaction-github-pages@v3.2.0
      with:
        repo: v2rayA/v2raya-apt
        target_branch: master
        build_dir: apt
      env:
          GH_PAT: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
    - name: AUR Release
      env:
        P_DIR: ${{ steps.prep.outputs.P_DIR }}
        SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        SIGNING_PASSWORD: ${{ secrets.SIGNING_PASSWORD }}
      run: |
        eval $(ssh-agent)
        echo "$SSH_PRIVATE_KEY" > ./key
        chmod 0600 ./key
        ./install/tool/ssh-add_expect ./key
        bash install/aur/deploy.sh
  Release_to_Homebrew:
    runs-on: ubuntu-22.04
    needs: [Build_v2rayA_Binaries]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      shell: bash
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Download Artifact Linux x64
      uses: actions/download-artifact@v4
      with:
        name: v2raya_linux_x64_${{ steps.prep.outputs.VERSION }}
        path: Downloads
    - name: Download Artifact macOS x64
      uses: actions/download-artifact@v4
      with:
        name: v2raya_darwin_x64_${{ steps.prep.outputs.VERSION }}
        path: Downloads
    - name: Download Artifact macOS arm64
      uses: actions/download-artifact@v4
      with:
        name: v2raya_darwin_arm64_${{ steps.prep.outputs.VERSION }}
        path: Downloads
    - name: Compress to Zip Files
      run: |
        mkdir v2raya-x86_64-linux; cp Downloads/v2raya_linux_x64_${{ steps.prep.outputs.VERSION }} ./v2raya-x86_64-linux/v2raya
        mkdir v2raya-x86_64-macos; cp Downloads/v2raya_darwin_x64_${{ steps.prep.outputs.VERSION }} ./v2raya-x86_64-macos/v2raya
        mkdir v2raya-aarch64-macos; cp Downloads/v2raya_darwin_arm64_${{ steps.prep.outputs.VERSION }} ./v2raya-aarch64-macos/v2raya
        zip -r9 v2raya-x86_64-linux.zip ./v2raya-x86_64-linux/*
        zip -r9 v2raya-x86_64-macos.zip ./v2raya-x86_64-macos/*
        zip -r9 v2raya-aarch64-macos.zip ./v2raya-aarch64-macos/*
        for file in $(ls v2raya-*.zip); do
          sha256sum $file > $file.sha256.txt
        done
    - name: Upload to Homebrew Tap
      env:
        GH_TOKEN: ${{ secrets.HOMEBREW_V2RAYA_TOKEN }}
      run: |
        tag_version=$(echo $VERSION | sed 's/v//g')
        gh release create $tag_version --repo v2rayA/homebrew-v2raya \
          --title "Releases $VERSION" --generate-notes
        gh release upload $tag_version --repo v2rayA/homebrew-v2raya \
          v2raya-x86_64-linux.zip v2raya-x86_64-linux.zip.sha256.txt \
          v2raya-x86_64-macos.zip v2raya-x86_64-macos.zip.sha256.txt \
          v2raya-aarch64-macos.zip v2raya-aarch64-macos.zip.sha256.txt
  Release_v2rayA_to_Docker:
    runs-on: ubuntu-22.04
    needs: [GitHub_Release]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
        IMAGE="mzz2017/v2raya"
        echo image=${IMAGE} >> $GITHUB_OUTPUT
        echo tag=${{ inputs.tag }} >> $GITHUB_OUTPUT
        sed -i "s|Realv2rayAVersion|$version|g" install/docker/docker_helper.sh
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3.0.0
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3.0.0
    - name: Login to GitHub container registry
      uses: docker/login-action@v3.0.0
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Login to DockerHub
      uses: docker/login-action@v3.0.0
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    - name: Convert repository owner to lowercase
      run: |
          echo "LOWERCASE_REPOSITORY_OWNER=${GITHUB_REPOSITORY_OWNER@L}" >>${GITHUB_ENV}
    - name: Build and push
      uses: docker/build-push-action@v5.0.0
      with:
        context: .
        builder: ${{ steps.buildx.outputs.name }}
        file: install/docker/Dockerfile.Action
        platforms: linux/arm,linux/arm64,linux/amd64,linux/riscv64
        push: true
        tags: |
          ${{ steps.prep.outputs.image }}:${{ steps.prep.outputs.tag }}
          ${{ steps.prep.outputs.image }}:latest
          ghcr.io/${{ env.LOWERCASE_REPOSITORY_OWNER }}/v2raya:${{ steps.prep.outputs.tag }}
          ghcr.io/${{ env.LOWERCASE_REPOSITORY_OWNER }}/v2raya:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
  Release_v2rayA_GUI_to_Docker:
    runs-on: ubuntu-22.04
    needs: [Build_v2rayA_Web]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Download Web Files
      uses: actions/download-artifact@v4
      with:
        name: web
        path: web
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3.0.0
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3.0.0
    - name: Login to GitHub container registry
      uses: docker/login-action@v3.0.0
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Login to DockerHub
      uses: docker/login-action@v3.0.0
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    - name: Convert repository owner to lowercase
      run: |
          echo "LOWERCASE_REPOSITORY_OWNER=${GITHUB_REPOSITORY_OWNER@L}" >>${GITHUB_ENV}
    - name: Build and push
      uses: docker/build-push-action@v5.0.0
      with:
        context: .
        builder: ${{ steps.buildx.outputs.name }}
        file: install/docker/Dockerfile.GUI.Action
        platforms: linux/arm,linux/arm64,linux/amd64,linux/riscv64
        push: true
        tags: |
          mzz2017/v2raya-gui:latest
          ghcr.io/${{ env.LOWERCASE_REPOSITORY_OWNER }}/v2raya-gui:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
  Submit_to_Microsoft_winget:
    runs-on: windows-latest
    needs: [GitHub_Release]
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Check Version
      id: prep
      shell: bash
      env:
        REF: ${{ inputs.tag }}
      run: |
        tag=${{ inputs.tag }}
        version=$(echo $tag | sed 's/v//g')
        echo "VERSION=$version" >> $GITHUB_OUTPUT
        echo "VERSION=$version" >> $GITHUB_ENV
    - name: Submit to Microsoft winget
      shell: pwsh
      run: |
        iwr https://aka.ms/wingetcreate/latest -OutFile wingetcreate.exe
        $Installer_x64_url = "https://github.com/v2rayA/v2rayA/releases/download/v${{ steps.prep.outputs.VERSION }}/installer_windows_inno_x64_${{ steps.prep.outputs.VERSION }}.exe"
        $Installer_ARM64_url = "https://github.com/v2rayA/v2rayA/releases/download/v${{ steps.prep.outputs.VERSION }}/installer_windows_inno_arm64_${{ steps.prep.outputs.VERSION }}.exe"
        ./wingetcreate.exe update v2rayA.v2rayA --urls $Installer_x64_url $Installer_ARM64_url --version ${{ steps.prep.outputs.VERSION }} --token ${{ secrets.HOMEBREW_V2RAYA_TOKEN }} --submit
