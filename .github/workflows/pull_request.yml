name: Pull Request Check
on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "**/*.go"
      - "service/**"
      - "gui/**"
      - ".github/workflows/*.yml"
jobs:
  Build_v2rayA_Web:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Set up Node.js
      shell: bash
      run: |
        eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
        brew install node@20
        echo "PATH=\"$(brew --prefix)/opt/node@20/bin:$PATH\"" >> $GITHUB_ENV
        echo "PATH=\"$(brew --prefix)/opt/node@20/bin:$PATH\"" >> ~/.bash_profile
    - name: Install Dependencies
      run: |
        sudo apt-get update -y && sudo apt-get install -y gzip
    - name: Build GUI
      run: |
        yarn --cwd gui --check-files
        yarn --cwd gui build
        echo "Use tar to generate web.tar.gz..."
        tar -zcvf web.tar.gz web/
    - name: Upload Zip File to Artifacts
      uses: actions/upload-artifact@v4
      with:
        path: web/*
        name: web
  
  Build_v2rayA:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
          os: [ubuntu-latest, windows-latest, macos-latest]
    needs: Build_v2rayA_Web
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Download Artifact
      uses: actions/download-artifact@v4
      with:
        name: web
        path: service/server/router/web
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        cache-dependency-path: |
          service/go.mod
          service/go.sum
        go-version: ^1.21
    - name: Build v2rayA
      shell: bash
      run: |
        mkdir v2raya_bin
        CurrentDir="$(pwd)"
        date=$(git -C "$CurrentDir" log -1 --format="%cd" --date=short | sed s/-//g)
        count=$(git -C "$CurrentDir" rev-list --count HEAD)
        commit=$(git -C "$CurrentDir" rev-parse --short HEAD)
        version="unstable-$date.r${count}.$commit"
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          win_ex_name=".exe"
          os="windows"
        elif [ "${{ matrix.os }}" = "macos-latest" ]; then
          os="darwin"
        elif [ "${{ matrix.os }}" = "ubuntu-latest" ]; then
          os="linux"
        fi
        cd "$CurrentDir"/service
        for arch in amd64 arm64; do
          GOARCH="$arch" CGO_ENABLED=0 go build -tags "with_gvisor" -ldflags "-X github.com/v2rayA/v2rayA/conf.Version=$version -s -w" -o "$CurrentDir"/v2raya_bin/v2raya_pr_build_"$os"_"$arch"_"$version""$win_ex_name"
        done
    - name: Upload Zip File to Artifacts
      uses: nanoufo/action-upload-artifacts-and-release-assets@v2
      with:
        path: v2raya_bin/*
