name: dispatch_docs

on:
  release:
    types:
      - released # a release or draft of a release is published, or a pre-release is changed to a release

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Invoke workflow in docs repo
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: Pages CI
          repo: v2rayA/v2raya.github.io
          ref: main
          token: ${{ secrets.DISPATCH_BUILD_TOKEN }}
